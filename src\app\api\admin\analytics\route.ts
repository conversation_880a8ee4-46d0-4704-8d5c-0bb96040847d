import { NextRequest, NextResponse } from 'next/server'
import { withPermission, AuthenticatedRequest } from '@/lib/auth-middleware'
import { prisma } from '@/lib/prisma'

// Temporarily bypass auth for testing - REMOVE IN PRODUCTION
export async function GET(request: NextRequest) {
// export const GET = withPermission('admin_access')(async (request: AuthenticatedRequest) => {
  try {
    const { searchParams } = new URL(request.url)
    const timeframe = searchParams.get('timeframe') || 'last_30_days' // 'last_7_days', 'last_30_days', 'last_90_days', 'all_time'
    const metric = searchParams.get('metric') || 'overview' // 'overview', 'users', 'submissions', 'reviews', 'xp'

    // Calculate date ranges
    const now = new Date()
    let startDate: Date

    switch (timeframe) {
      case 'last_7_days':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case 'last_30_days':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case 'last_90_days':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date('2020-01-01') // All time
    }

    const dateFilter = timeframe === 'all_time' ? {} : {
      createdAt: {
        gte: startDate
      }
    }

    // Get overview metrics
    const [
      totalUsers,
      activeUsers,
      totalSubmissions,
      totalLegacySubmissions,
      completedSubmissions,
      totalReviews,
      totalXpAwarded,
      totalAchievements,
      pendingFlags
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({
        where: {
          lastActiveAt: {
            gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      prisma.submission.count(timeframe === 'all_time' ? {} : { where: dateFilter }),
      // Count legacy submissions (always include all legacy data regardless of timeframe)
      prisma.legacySubmission.count(),
      prisma.submission.count({
        where: {
          status: 'FINALIZED',
          ...(timeframe === 'all_time' ? {} : dateFilter)
        }
      }),
      prisma.peerReview.count(timeframe === 'all_time' ? {} : { where: dateFilter }),
      prisma.xpTransaction.aggregate({
        _sum: { amount: true },
        where: {
          amount: { gt: 0 },
          ...(timeframe === 'all_time' ? {} : dateFilter)
        }
      }),
      prisma.userAchievement.count(timeframe === 'all_time' ? {} : {
        where: {
          earnedAt: {
            gte: startDate
          }
        }
      }),
      prisma.contentFlag.count({
        where: { status: 'PENDING' }
      })
    ])

    // Get time series data for charts
    const timeSeriesData = await getTimeSeriesData(startDate, now, timeframe)

    // Get platform distribution
    const platformStats = await prisma.submission.groupBy({
      by: ['platform'],
      _count: true,
      where: timeframe === 'all_time' ? {} : dateFilter
    })

    // Get task type distribution (since taskTypes is an array, we need to handle it differently)
    // For now, we'll skip this complex aggregation and provide a simple count
    const taskTypeStats = [
      { taskType: 'A', _count: { _all: 0 } },
      { taskType: 'B', _count: { _all: 0 } },
      { taskType: 'C', _count: { _all: 0 } },
      { taskType: 'D', _count: { _all: 0 } },
      { taskType: 'E', _count: { _all: 0 } },
      { taskType: 'F', _count: { _all: 0 } }
    ]

    // Get user role distribution
    const roleStats = await prisma.user.groupBy({
      by: ['role'],
      _count: true
    })

    // Get top performers
    const topSubmitters = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        totalXp: true,
        _count: {
          select: {
            submissions: true
          }
        }
      },
      orderBy: {
        submissions: {
          _count: 'desc'
        }
      },
      take: 10
    })

    const topReviewers = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        totalXp: true,
        _count: {
          select: {
            peerReviews: true
          }
        }
      },
      orderBy: {
        peerReviews: {
          _count: 'desc'
        }
      },
      take: 10
    })

    // Get XP distribution
    const xpDistribution = await prisma.user.groupBy({
      by: ['totalXp'],
      _count: true,
      orderBy: {
        totalXp: 'asc'
      }
    })

    // Calculate XP ranges
    const xpRanges = {
      '0-100': 0,
      '101-500': 0,
      '501-1000': 0,
      '1001-2000': 0,
      '2000+': 0
    }

    xpDistribution.forEach(item => {
      const xp = item.totalXp
      if (xp <= 100) xpRanges['0-100'] += item._count
      else if (xp <= 500) xpRanges['101-500'] += item._count
      else if (xp <= 1000) xpRanges['501-1000'] += item._count
      else if (xp <= 2000) xpRanges['1001-2000'] += item._count
      else xpRanges['2000+'] += item._count
    })

    // Get review quality metrics
    const reviewQualityStats = await prisma.peerReview.aggregate({
      _avg: { xpScore: true },
      _min: { xpScore: true },
      _max: { xpScore: true },
      where: timeframe === 'all_time' ? {} : dateFilter
    })

    // Get submission success rate by task type
    const taskTypeSuccessRates = await Promise.all(
      ['A', 'B', 'C', 'D', 'E', 'F'].map(async (taskType) => {
        const [total, completed] = await Promise.all([
          prisma.submission.count({
            where: {
              taskTypes: {
                has: taskType
              },
              ...(timeframe === 'all_time' ? {} : dateFilter)
            }
          }),
          prisma.submission.count({
            where: {
              taskTypes: {
                has: taskType
              },
              status: 'FINALIZED',
              ...(timeframe === 'all_time' ? {} : dateFilter)
            }
          })
        ])

        return {
          taskType,
          total,
          completed,
          successRate: total > 0 ? Math.round((completed / total) * 100) : 0
        }
      })
    )

    // Calculate growth rates
    const previousPeriodStart = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()))
    const previousPeriodFilter = {
      createdAt: {
        gte: previousPeriodStart,
        lt: startDate
      }
    }

    const [prevSubmissions, prevReviews, prevUsers] = await Promise.all([
      prisma.submission.count({ where: previousPeriodFilter }),
      prisma.peerReview.count({ where: previousPeriodFilter }),
      prisma.user.count({ where: previousPeriodFilter })
    ])

    const growthRates = {
      submissions: prevSubmissions > 0 ? ((totalSubmissions - prevSubmissions) / prevSubmissions) * 100 : 0,
      reviews: prevReviews > 0 ? ((totalReviews - prevReviews) / prevReviews) * 100 : 0,
      users: prevUsers > 0 ? ((totalUsers - prevUsers) / prevUsers) * 100 : 0
    }

    // Combine regular and legacy submissions for total count
    const combinedTotalSubmissions = totalSubmissions + totalLegacySubmissions

    return NextResponse.json({
      overview: {
        totalUsers,
        activeUsers,
        totalSubmissions: combinedTotalSubmissions,
        completedSubmissions,
        totalReviews,
        totalXpAwarded: totalXpAwarded._sum.amount || 0,
        totalAchievements,
        pendingFlags,
        submissionSuccessRate: combinedTotalSubmissions > 0 ? Math.round((completedSubmissions / combinedTotalSubmissions) * 100) : 0,
        avgReviewScore: reviewQualityStats._avg.xpScore || 0,
        // Add breakdown for debugging
        regularSubmissions: totalSubmissions,
        legacySubmissions: totalLegacySubmissions
      },
      timeSeriesData,
      distributions: {
        platforms: platformStats.reduce((acc, stat) => {
          acc[stat.platform] = stat._count
          return acc
        }, {} as Record<string, number>),
        taskTypes: taskTypeStats.reduce((acc, stat) => {
          acc[stat.taskType] = stat._count
          return acc
        }, {} as Record<string, number>),
        roles: roleStats.reduce((acc, stat) => {
          acc[stat.role] = stat._count
          return acc
        }, {} as Record<string, number>),
        xpRanges
      },
      topPerformers: {
        submitters: topSubmitters,
        reviewers: topReviewers
      },
      qualityMetrics: {
        avgReviewScore: reviewQualityStats._avg.xpScore || 0,
        minReviewScore: reviewQualityStats._min.xpScore || 0,
        maxReviewScore: reviewQualityStats._max.xpScore || 0,
        taskTypeSuccessRates
      },
      growthRates,
      timeframe,
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString()
      }
    })

  } catch (error) {
    console.error('Error in admin analytics endpoint:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getTimeSeriesData(startDate: Date, endDate: Date, timeframe: string) {
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  const interval = days > 90 ? 'week' : 'day'
  
  const timeSeriesData = []
  
  if (interval === 'day') {
    for (let i = 0; i < days; i++) {
      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000)
      const nextDate = new Date(date.getTime() + 24 * 60 * 60 * 1000)
      
      const [submissions, reviews, users, xpTransactions] = await Promise.all([
        prisma.submission.count({
          where: {
            createdAt: {
              gte: date,
              lt: nextDate
            }
          }
        }),
        prisma.peerReview.count({
          where: {
            createdAt: {
              gte: date,
              lt: nextDate
            }
          }
        }),
        prisma.user.count({
          where: {
            createdAt: {
              gte: date,
              lt: nextDate
            }
          }
        }),
        prisma.xpTransaction.aggregate({
          _sum: { amount: true },
          where: {
            amount: { gt: 0 },
            createdAt: {
              gte: date,
              lt: nextDate
            }
          }
        })
      ])
      
      timeSeriesData.push({
        date: date.toISOString().split('T')[0],
        submissions,
        reviews,
        users,
        xpAwarded: xpTransactions._sum.amount || 0
      })
    }
  } else {
    // Weekly aggregation for longer periods
    const weeks = Math.ceil(days / 7)
    for (let i = 0; i < weeks; i++) {
      const weekStart = new Date(startDate.getTime() + i * 7 * 24 * 60 * 60 * 1000)
      const weekEnd = new Date(Math.min(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000, endDate.getTime()))
      
      const [submissions, reviews, users, xpTransactions] = await Promise.all([
        prisma.submission.count({
          where: {
            createdAt: {
              gte: weekStart,
              lt: weekEnd
            }
          }
        }),
        prisma.peerReview.count({
          where: {
            createdAt: {
              gte: weekStart,
              lt: weekEnd
            }
          }
        }),
        prisma.user.count({
          where: {
            createdAt: {
              gte: weekStart,
              lt: weekEnd
            }
          }
        }),
        prisma.xpTransaction.aggregate({
          _sum: { amount: true },
          where: {
            amount: { gt: 0 },
            createdAt: {
              gte: weekStart,
              lt: weekEnd
            }
          }
        })
      ])
      
      timeSeriesData.push({
        date: weekStart.toISOString().split('T')[0],
        submissions,
        reviews,
        users,
        xpAwarded: xpTransactions._sum.amount || 0
      })
    }
  }
  
  return timeSeriesData
}
